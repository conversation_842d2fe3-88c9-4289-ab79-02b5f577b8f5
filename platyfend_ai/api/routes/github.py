import logging

from fastapi import APIRouter, Body, Depends, File, HTTPException, Query, UploadFile
from fastapi.responses import JSONResponse

from platyfend_ai.utils.security_service import SecurityService

logger = logging.getLogger(__name__)

github_routes = APIRouter(
    tags=["Methods"], dependencies=[Depends(SecurityService.get_api_key)]
)


@github_routes.post("/pr_open")
def github_pr_open():
    pass
